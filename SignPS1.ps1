# Load the certificate (will prompt for password if needed)
$cert = Get-PfxCertificate -FilePath "MyCodeSigningCert.pfx"

# Sign the PS1 file
Set-AuthenticodeSignature -FilePath "CREATE - List of Files in Directory.ps1" -Certificate $cert

# Then convert to EXE with ps2exe
# ps2exe '.\MyBillingScript.ps1' -noConsole -noOutput -title "My Monthly Bills" -product "My Monthly Billing" -copyright "Copyright (c) 2025, Adam Frang<PERSON>" -iconFile "PanAura.ico" -version "4.5.6"
